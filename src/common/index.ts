// DTOs
export * from './dto/pagination.dto';

// Logger
export * from './logger/logger.service';
export * from './logger/logger.decorator';
export * from './logger/http-logging.interceptor';

// Constants
export * from './constants/permissions.constants';

// Utils
export * from './utils/permission.utils';

// Types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  success: boolean;
  timestamp?: string;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error?: string;
  details?: any;
  timestamp?: string;
}

// Constants
export const DEFAULT_PAGE_SIZE = 10;
export const MAX_PAGE_SIZE = 100;
