import { User, Permission, Role } from '../../entities';
import { PERMISSIONS, hasPermission, hasAnyPermission, hasAllPermissions } from '@common/constants/permissions';

/**
 * Extract all permissions from user's roles and groups
 */
export function extractUserPermissions(user: User): string[] {
  const permissions = new Set<string>();

  // Add permissions from roles
  if (user.roles) {
    user.roles.forEach(role => {
      if (role.permissions) {
        role.permissions.forEach(permission => {
          permissions.add(`${permission.action}:${permission.resource}`);
        });
      }
    });
  }

  // Add permissions from groups
  if (user.groups) {
    user.groups.forEach(group => {
      // Direct group permissions
      if (group.permissions) {
        group.permissions.forEach(permission => {
          permissions.add(`${permission.action}:${permission.resource}`);
        });
      }

      // Permissions from group roles
      if (group.roles) {
        group.roles.forEach(role => {
          if (role.permissions) {
            role.permissions.forEach(permission => {
              permissions.add(`${permission.action}:${permission.resource}`);
            });
          }
        });
      }
    });
  }

  return Array.from(permissions);
}

/**
 * Check if user has specific permission
 */
export function userHasPermission(user: User, permission: string): boolean {
  const userPermissions = extractUserPermissions(user);
  return hasPermission(userPermissions, permission);
}

/**
 * Check if user has any of the specified permissions
 */
export function userHasAnyPermission(user: User, permissions: string[]): boolean {
  const userPermissions = extractUserPermissions(user);
  return hasAnyPermission(userPermissions, permissions);
}

/**
 * Check if user has all of the specified permissions
 */
export function userHasAllPermissions(user: User, permissions: string[]): boolean {
  const userPermissions = extractUserPermissions(user);
  return hasAllPermissions(userPermissions, permissions);
}

/**
 * Check if user has super admin privileges
 */
export function isSuperAdmin(user: User): boolean {
  return userHasPermission(user, PERMISSIONS.ALL_MANAGE);
}

/**
 * Check if user is department manager
 */
export function isDepartmentManager(user: User): boolean {
  return user.managedDepartments && user.managedDepartments.length > 0;
}

/**
 * Check if user is team leader
 */
export function isTeamLeader(user: User): boolean {
  return user.ledTeams && user.ledTeams.length > 0;
}

/**
 * Check if user can manage specific department
 */
export function canManageDepartment(user: User, departmentId: string): boolean {
  if (isSuperAdmin(user)) return true;
  return user.managedDepartments?.some(dept => dept.id === departmentId) || false;
}

/**
 * Check if user can manage specific team
 */
export function canManageTeam(user: User, teamId: string): boolean {
  if (isSuperAdmin(user)) return true;
  
  // Check if user leads this team
  if (user.ledTeams?.some(team => team.id === teamId)) return true;
  
  // Check if user manages the department this team belongs to
  if (user.managedDepartments) {
    return user.managedDepartments.some(dept => 
      dept.teams?.some(team => team.id === teamId)
    );
  }
  
  return false;
}

/**
 * Check if user can access specific department
 */
export function canAccessDepartment(user: User, departmentId: string): boolean {
  if (isSuperAdmin(user)) return true;
  if (canManageDepartment(user, departmentId)) return true;
  return user.departmentId === departmentId;
}

/**
 * Check if user can access specific team
 */
export function canAccessTeam(user: User, teamId: string): boolean {
  if (isSuperAdmin(user)) return true;
  if (canManageTeam(user, teamId)) return true;
  return user.teamId === teamId;
}

/**
 * Get user's hierarchy level
 */
export function getUserHierarchyLevel(user: User): 'super_admin' | 'department_manager' | 'team_leader' | 'team_member' | 'user' {
  if (isSuperAdmin(user)) return 'super_admin';
  if (isDepartmentManager(user)) return 'department_manager';
  if (isTeamLeader(user)) return 'team_leader';
  if (user.teamId) return 'team_member';
  return 'user';
}

/**
 * Check if user can assign specific role
 */
export function canAssignRole(user: User, roleToAssign: Role): boolean {
  if (isSuperAdmin(user)) return true;
  
  const userLevel = getUserHierarchyLevel(user);
  const roleLevel = Role.getHierarchyLevel(roleToAssign.type);
  const userRoleLevel = user.roles?.[0] ? Role.getHierarchyLevel(user.roles[0].type) : 999;
  
  // User can only assign roles that are lower in hierarchy than their own
  return userRoleLevel < roleLevel;
}

/**
 * Filter permissions based on user's access level
 */
export function filterPermissionsByUserLevel(user: User, permissions: Permission[]): Permission[] {
  if (isSuperAdmin(user)) return permissions;
  
  const userLevel = getUserHierarchyLevel(user);
  
  // Define which permissions each level can see/manage
  const levelPermissions = {
    department_manager: [
      'user', 'team', 'content', 'reports', 'analytics'
    ],
    team_leader: [
      'user', 'content', 'reports'
    ],
    team_member: [
      'content', 'profile'
    ],
    user: [
      'profile'
    ]
  };
  
  const allowedResources = levelPermissions[userLevel] || [];
  
  return permissions.filter(permission => 
    allowedResources.includes(permission.resource)
  );
}

/**
 * Get permissions that user can grant to others
 */
export function getGrantablePermissions(user: User, allPermissions: Permission[]): Permission[] {
  const userPermissions = extractUserPermissions(user);
  
  return allPermissions.filter(permission => {
    const permissionString = `${permission.action}:${permission.resource}`;
    
    // User can grant permissions they have, except manage:all
    if (permissionString === PERMISSIONS.ALL_MANAGE) {
      return isSuperAdmin(user);
    }
    
    return hasPermission(userPermissions, permissionString);
  });
}
